import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import { adminUserAPI } from '@/api'

/**
 * 后台用户列表页面组合式函数
 * @returns {Object} 返回模板需要的所有数据和方法
 */
export function useAdminUserList() {
  const router = useRouter()

  // 分页状态
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const loading = ref(false)

  // 搜索表单数据
  const searchFormData = reactive({
    username: '',
    login_status: ''
  })

  // 表格配置
  const tableConfig = {
    columns: [
      { prop: 'id', label: 'ID'},
      { prop: 'username', label: '用户名'},
      {
        prop: 'login_status',
        label: '登录状态',
        type: 'slot',
      },
      { prop: 'last_login_time', label: '最后登录时间', type: 'datetime'},
      // { prop: 'login_count', label: '登录次数', width: 120 },
      { prop: 'create_time', label: '创建时间', type: 'datetime'},
      { prop: 'update_time', label: '更新时间', type: 'datetime'}
    ],
    actions: [
      // { type: 'view', label: '查看', style: 'text' },
      { type: 'edit', label: '编辑', style: 'primary' },
      // { type: 'resetPassword', label: '重置密码', style: 'warning' },
      { type: 'delete', label: '删除', style: 'danger' }
    ],
    pagination: true,
    selection: false
  }

  // 表格数据
  const tableData = ref([])

  // 页面初始化状态标记
  const isInitialized = ref(false)

  // 模拟后台用户数据
  const mockAdminUsers = [
    {
      id: 1,
      username: 'admin',
      login_status: '1',
      last_login_time: '2024-06-19 10:30:00',
      login_count: 156,
      create_time: '2024-01-01 10:00:00',
      update_time: '2024-06-19 10:30:00'
    },
    {
      id: 2,
      username: 'manager',
      login_status: '0',
      last_login_time: '2024-06-18 16:45:00',
      login_count: 89,
      create_time: '2024-01-15 14:20:00',
      update_time: '2024-06-18 16:45:00'
    },
    {
      id: 3,
      username: 'operator',
      login_status: '1',
      last_login_time: '2024-06-19 09:15:00',
      login_count: 234,
      create_time: '2024-02-01 09:15:00',
      update_time: '2024-06-19 09:15:00'
    },
    {
      id: 4,
      username: 'support',
      login_status: '0',
      last_login_time: '2024-06-17 14:30:00',
      login_count: 67,
      create_time: '2024-03-10 16:30:00',
      update_time: '2024-06-17 14:30:00'
    },
    {
      id: 5,
      username: 'editor',
      login_status: '1',
      last_login_time: '2024-06-19 08:45:00',
      login_count: 123,
      create_time: '2024-04-05 11:45:00',
      update_time: '2024-06-19 08:45:00'
    }
  ]

  // 获取后台用户列表
  const fetchAdminUserList = async () => {
    loading.value = true
    try {
      const params = {
        page: currentPage.value,
        limit: pageSize.value,
        ...searchFormData
      }

      const response = await adminUserAPI.getAdminUserList(params)

      if (response && response.data) {
        tableData.value = response.data.list || []
        total.value = response.data.total || 0
      } else {
        tableData.value = response.list || response || []
        total.value = response.total || tableData.value.length
      }

      // 数据加载完成后，延迟设置初始化标记，避免开关组件初始化触发事件
      setTimeout(() => {
        isInitialized.value = true
      }, 100)

    } catch (error) {
      console.error('获取后台用户列表失败:', error)
      ElMessage.error('获取后台用户列表失败，使用模拟数据')

      // 如果API失败，使用模拟数据作为备用
      let filteredData = [...mockAdminUsers]

      if (searchFormData.username) {
        filteredData = filteredData.filter(user =>
          user.username.includes(searchFormData.username)
        )
      }

      if (searchFormData.login_status) {
        filteredData = filteredData.filter(user =>
          user.login_status === searchFormData.login_status
        )
      }

      // 模拟分页
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value

      tableData.value = filteredData.slice(start, end)
      total.value = filteredData.length
    } finally {
      loading.value = false
      // 数据加载完成后，延迟设置初始化标记，避免开关组件初始化触发事件
      setTimeout(() => {
        isInitialized.value = true
      }, 100)
    }
  }

  // 搜索用户
  const handleSearch = () => {
    // 重置初始化标记，避免开关组件触发事件
    isInitialized.value = false
    currentPage.value = 1
    fetchAdminUserList()
    ElMessage.success('搜索完成')
  }

  // 重置搜索
  const handleReset = () => {
    // 重置初始化标记，避免开关组件触发事件
    isInitialized.value = false
    Object.assign(searchFormData, {
      username: '',
      login_status: ''
    })
    currentPage.value = 1
    fetchAdminUserList()
    ElMessage.info('已重置搜索条件')
  }

  // 新增管理员
  const handleAdd = () => {
    router.push('/admin/admin-user-list/admin-user-create')
  }

  // 表格操作处理
  const handleTableAction = ({ type, row }) => {
    switch (type) {
      case 'view':
        ElMessage.info(`查看管理员：${row.username}`)
        // router.push(`/admin/admin-user-edit/${row.id}?mode=view`)
        break
      case 'edit':
        ElMessage.success(`编辑管理员：${row.username}`)
        // router.push(`/admin/admin-user-edit/${row.id}`)
        break
      case 'resetPassword':
        handleResetPassword(row)
        break
      case 'delete':
        handleDelete(row)
        break
      default:
        ElMessage.warning(`未知操作：${type}`)
    }
  }

  // 处理登录状态变化
  const handleLoginStatusChange = async (value, row) => {
    try {
      console.log('登录状态变化:', { value, row, isInitialized: isInitialized.value })

      // 防止初始化时触发
      if (!isInitialized.value) {
        console.log('页面初始化中，忽略开关变化事件')
        return
      }

      // 检查值是否真的发生了变化
      if (row.login_status === value) {
        console.log('状态值未发生变化，忽略事件')
        return
      }

      // 显示确认对话框
      const action = value === '1' ? '启用' : '禁用'
      await ElMessageBox.confirm(
        `确定要${action}管理员 "${row.username}" 的登录状态吗？`,
        '状态变更确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 模拟API调用
      loading.value = true
      await new Promise(resolve => setTimeout(resolve, 500))

      // 更新本地数据
      const targetRow = tableData.value.find(item => item.id === row.id)
      if (targetRow) {
        targetRow.login_status = value
      }

      ElMessage.success(`管理员 "${row.username}" 登录状态已${action}`)

    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('状态变更失败')
        console.error('登录状态变化处理失败:', error)
      }
      // 如果操作失败或取消，刷新数据恢复原状态
      fetchAdminUserList()
    } finally {
      loading.value = false
    }
  }

  // 重置密码
  const handleResetPassword = async (row) => {
    try {
      await ElMessageBox.confirm(
        `确定要重置管理员 "${row.username}" 的密码吗？`,
        '重置密码确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      // 模拟重置密码API
      await new Promise(resolve => setTimeout(resolve, 500))
      
      ElMessage.success(`管理员 "${row.username}" 密码重置成功，新密码已发送至邮箱`)
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('重置密码失败')
      }
    }
  }

  // 删除管理员
  const handleDelete = async (row) => {
    if (row.username === 'admin') {
      ElMessage.warning('超级管理员账号不能删除')
      return
    }

    try {
      await ElMessageBox.confirm(
        `确定要删除管理员 "${row.username}" 吗？`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      // 模拟删除API
      await new Promise(resolve => setTimeout(resolve, 500))
      
      ElMessage.success(`管理员 "${row.username}" 删除成功`)
      fetchAdminUserList()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 选择变化
  const handleSelectionChange = (selection) => {
    console.log('选中的管理员：', selection)
    if (selection.length > 0) {
      ElMessage.info(`已选中 ${selection.length} 个管理员`)
    }
  }

  // 分页改变
  const handlePageChange = (page) => {
    // 重置初始化标记，避免开关组件触发事件
    isInitialized.value = false
    currentPage.value = page
    fetchAdminUserList()
  }

  // 页面大小改变
  const handleSizeChange = (size) => {
    // 重置初始化标记，避免开关组件触发事件
    isInitialized.value = false
    pageSize.value = size
    currentPage.value = 1
    fetchAdminUserList()
  }

  // 页面加载时获取数据
  onMounted(() => {
    fetchAdminUserList()
  })

  // 返回模板需要的所有数据和方法
  return {
    // 图标组件
    Plus,
    Search,
    Refresh,
    // 响应式数据
    searchFormData,
    tableData,
    tableConfig,
    loading,
    total,
    currentPage,
    pageSize,
    // 方法
    handleAdd,
    handleSearch,
    handleReset,
    handleTableAction,
    handleLoginStatusChange,
    handleSelectionChange,
    handlePageChange,
    handleSizeChange
  }
}
