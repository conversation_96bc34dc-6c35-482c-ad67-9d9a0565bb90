/**
 * 权限管理工具
 * 提供权限检查、角色验证等功能
 */

import {
  PERMISSIONS,
  ROLE_PERMISSIONS,
  ROLE_CONFIG,
  PERMISSION_NAMES,
  PERMISSION_GROUPS,
  DEFAULT_CONFIG
} from '@/config/permissions'

// 重新导出权限配置，方便其他地方使用
export {
  PERMISSIONS,
  ROLE_PERMISSIONS,
  ROLE_CONFIG,
  PERMISSION_NAMES,
  PERMISSION_GROUPS,
  DEFAULT_CONFIG
}

// 获取当前用户信息
export function getCurrentUser() {
  const userStr = localStorage.getItem('userInfo')
  const user = userStr ? JSON.parse(userStr) : null

  // 如果有用户信息，直接返回
  if (user) {
    return user
  }

  // 检查是否有 token，如果有说明可能通过 cookie 认证但用户信息未保存
  const token = localStorage.getItem('token')
  if (token) {
    // 可以考虑从后端重新获取用户信息
    console.warn('有token但无用户信息，建议重新获取用户信息')
  }

  // 如果没有用户信息，返回默认admin用户（开发模式）
  if (import.meta.env.DEV) {
    return {
      id: 1,
      username: 'admin',
      role: 'admin',
      name: '管理员'
    }
  }

  return null
}

// 获取当前用户角色
export function getCurrentUserRole() {
  const user = getCurrentUser()
  const role = user ? user.role : 'admin' // 默认为admin
  return role
}

// 获取当前用户权限列表
export function getCurrentUserPermissions() {
  const role = getCurrentUserRole()
  const permissions = role ? ROLE_PERMISSIONS[role] || [] : []
  return permissions
}

// 检查用户是否有指定权限（支持通配符 *）
export function hasPermission(permission) {
  const permissions = getCurrentUserPermissions()

  // 直接匹配
  if (permissions.includes(permission)) {
    return true
  }

  // 检查是否有通配符权限
  if (permissions.includes('*')) {
    return true
  }

  // 检查模块级通配符，例如 user:* 匹配 user:view, user:create 等
  const [module] = permission.split(':')
  if (module && permissions.includes(`${module}:*`)) {
    return true
  }

  return false
}

// 检查用户是否有任意一个权限
export function hasAnyPermission(permissionList) {
  if (!Array.isArray(permissionList)) {
    return false
  }
  
  const userPermissions = getCurrentUserPermissions()
  return permissionList.some(permission => userPermissions.includes(permission))
}

// 检查用户是否有所有权限
export function hasAllPermissions(permissionList) {
  if (!Array.isArray(permissionList)) {
    return false
  }
  
  const userPermissions = getCurrentUserPermissions()
  return permissionList.every(permission => userPermissions.includes(permission))
}

// 检查用户角色
export function hasRole(role) {
  const currentRole = getCurrentUserRole()
  return currentRole === role
}

// 检查用户是否有任意一个角色
export function hasAnyRole(roleList) {
  if (!Array.isArray(roleList)) {
    return false
  }
  
  const currentRole = getCurrentUserRole()
  return roleList.includes(currentRole)
}

// 权限过滤器 - 过滤菜单项
export function filterMenuByPermission(menuList, requiredPermission) {
  if (!Array.isArray(menuList)) {
    return []
  }

  // 获取当前用户角色
  const currentRole = getCurrentUserRole()

  // 如果是超级管理员或管理员，显示所有菜单
  if (currentRole === 'superadmin' || currentRole === 'admin') {
    // 递归处理子菜单
    return menuList.map(menu => ({
      ...menu,
      children: menu.children && Array.isArray(menu.children)
        ? filterMenuByPermission(menu.children)
        : menu.children
    }))
  }

  return menuList.filter(menu => {
    // 如果菜单项没有权限要求，则显示
    if (!menu.permission) {
      return true
    }

    // 检查是否有权限
    let hasAccess = false

    if (typeof menu.permission === 'string') {
      hasAccess = hasPermission(menu.permission)
    } else if (Array.isArray(menu.permission)) {
      hasAccess = hasAnyPermission(menu.permission)
    }

    // 如果有子菜单，递归过滤
    if (menu.children && Array.isArray(menu.children)) {
      const filteredChildren = filterMenuByPermission(menu.children)

      // 如果有权限或者有可访问的子菜单，则显示
      if (hasAccess || filteredChildren.length > 0) {
        menu.children = filteredChildren
        return true
      }
    }

    return hasAccess
  })
}

// 权限指令值解析
export function parsePermissionDirective(value) {
  if (typeof value === 'string') {
    return { type: 'permission', value: [value] }
  }
  
  if (Array.isArray(value)) {
    return { type: 'permission', value: value }
  }
  
  if (typeof value === 'object' && value !== null) {
    return {
      type: value.type || 'permission',
      value: Array.isArray(value.value) ? value.value : [value.value],
      mode: value.mode || 'any' // 'any' 或 'all'
    }
  }
  
  return { type: 'permission', value: [] }
}

// 检查权限指令
export function checkPermissionDirective(directiveValue) {
  const parsed = parsePermissionDirective(directiveValue)
  
  switch (parsed.type) {
    case 'role':
      return parsed.mode === 'all' 
        ? hasAllRoles(parsed.value)
        : hasAnyRole(parsed.value)
        
    case 'permission':
    default:
      return parsed.mode === 'all'
        ? hasAllPermissions(parsed.value)
        : hasAnyPermission(parsed.value)
  }
}

// 权限装饰器 - 用于组件方法
export function requirePermission(permission) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = function(...args) {
      if (!hasPermission(permission)) {
        console.warn(`权限不足，需要权限: ${permission}`)
        return false
      }
      
      return originalMethod.apply(this, args)
    }
    
    return descriptor
  }
}

// 权限提示信息
export function getPermissionMessage(permission) {
  return PERMISSION_NAMES[permission] || '未知权限'
}

// 获取角色配置信息
export function getRoleConfig(role) {
  return ROLE_CONFIG[role] || null
}

// 获取角色权限列表
export function getRolePermissions(role) {
  return ROLE_PERMISSIONS[role] || []
}

// 获取权限分组信息
export function getPermissionGroups() {
  return PERMISSION_GROUPS
}

// 检查权限继承关系
export function checkPermissionInheritance(permission) {
  const inheritance = DEFAULT_CONFIG.PERMISSION_INHERITANCE
  return inheritance[permission] || []
}

// 获取用户有效权限（包括继承的权限）
export function getUserEffectivePermissions() {
  const userPermissions = getCurrentUserPermissions()
  const effectivePermissions = new Set(userPermissions)
  
  // 添加继承的权限
  userPermissions.forEach(permission => {
    const inheritedPermissions = checkPermissionInheritance(permission)
    inheritedPermissions.forEach(inherited => {
      effectivePermissions.add(inherited)
    })
  })
  
  return Array.from(effectivePermissions)
}

// 检查权限级别
export function hasPermissionLevel(requiredLevel) {
  const currentRole = getCurrentUserRole()
  const roleConfig = getRoleConfig(currentRole)
  
  if (!roleConfig) return false
  
  return roleConfig.level <= requiredLevel
}

// 获取用户角色级别
export function getUserRoleLevel() {
  const currentRole = getCurrentUserRole()
  const roleConfig = getRoleConfig(currentRole)
  
  return roleConfig ? roleConfig.level : 999
}

// 权限检查失败时的处理 - 只弹窗不重定向
export function handlePermissionDenied(permission, options = {}) {
  const {
    showDialog = true,
    title = '权限不足',
    message = null
  } = options
  
  if (showDialog) {
    // 显示权限不足弹窗
    const permissionName = getPermissionMessage(permission)
    const alertMessage = message || `您没有执行此操作的权限。\n需要权限：${permissionName}`
    
    import('element-plus').then(({ ElMessageBox }) => {
      ElMessageBox.alert(
        alertMessage,
        title,
        {
          confirmButtonText: '知道了',
          type: 'warning',
          center: true,
          customClass: 'permission-denied-dialog'
        }
      )
    })
  }
  
  return false
}

// 带权限检查的方法装饰器
export function withPermissionCheck(permission, options = {}) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = function(...args) {
      if (!hasPermission(permission)) {
        handlePermissionDenied(permission, options)
        return false
      }
      
      return originalMethod.apply(this, args)
    }
    
    return descriptor
  }
}

// 权限检查组合式函数
export function usePermissionCheck() {
  const checkPermission = (permission, showDialog = true) => {
    if (hasPermission(permission)) {
      return true
    }
    
    if (showDialog) {
      handlePermissionDenied(permission)
    }
    
    return false
  }
  
  const checkAnyPermission = (permissions, showDialog = true) => {
    if (hasAnyPermission(permissions)) {
      return true
    }
    
    if (showDialog) {
      const permissionNames = permissions.map(p => getPermissionMessage(p)).join('、')
      handlePermissionDenied(permissions[0], {
        showDialog: true,
        title: '权限不足',
        message: `您没有执行此操作的权限。\n需要权限（任一）：${permissionNames}`
      })
    }
    
    return false
  }
  
  return {
    checkPermission,
    checkAnyPermission,
    hasPermission,
    hasAnyPermission
  }
}

// 检查所有角色 - 辅助函数
function hasAllRoles(roleList) {
  if (!Array.isArray(roleList)) {
    return false
  }
  
  const currentRole = getCurrentUserRole()
  // 注意：用户通常只有一个角色，所以这里的逻辑可能需要根据实际业务调整
  return roleList.length === 1 && roleList.includes(currentRole)
}
